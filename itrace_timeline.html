
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ITrace 指令执行时间线</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .header h1 {
            margin: 0;
            font-size: 2.5em;
            text-align: center;
        }

        .stats {
            display: flex;
            justify-content: space-around;
            margin-top: 15px;
            font-size: 1.1em;
        }

        .timeline-container {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow-x: auto;
        }

        .timeline-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .legend {
            display: flex;
            gap: 20px;
            margin-left: auto;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
        }

        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 4px;
        }

        .time-axis {
            position: relative;
            height: 40px;
            margin-bottom: 20px;
            border-bottom: 2px solid #ddd;
            min-width: 800px;
            overflow-x: auto;
        }

        .time-tick {
            position: absolute;
            bottom: 0;
            width: 1px;
            height: 10px;
            background: #666;
        }

        .time-label {
            position: absolute;
            bottom: -25px;
            font-size: 12px;
            color: #666;
            transform: translateX(-50%);
        }

        .instruction-row {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px;
            background: #fafafa;
            border-radius: 8px;
            border-left: 4px solid #007bff;
            transition: all 0.3s ease;
        }

        .instruction-row:hover {
            background: #e3f2fd;
            transform: translateX(5px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }

        .instruction-info {
            width: 300px;
            flex-shrink: 0;
            padding-right: 20px;
        }

        .instruction-id {
            font-weight: bold;
            color: #007bff;
            font-size: 1.1em;
        }

        .instruction-pc {
            color: #666;
            font-family: monospace;
            font-size: 0.9em;
        }

        .instruction-disasm {
            color: #333;
            font-family: monospace;
            font-size: 0.9em;
            margin-top: 5px;
            word-break: break-all;
        }

        .timeline-bars {
            flex: 1;
            position: relative;
            height: 60px;
            min-width: 800px;
        }

        .stage-bar {
            position: absolute;
            height: 12px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: bold;
            color: white;
            text-shadow: 1px 1px 1px rgba(0,0,0,0.5);
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .stage-bar:hover {
            transform: scaleY(1.2);
            z-index: 10;
        }

        .stage-bar.fetch { top: 5px; }
        .stage-bar.decode { top: 20px; }
        .stage-bar.dispatch { top: 35px; }
        .stage-bar.execute { top: 50px; }

        .tooltip {
            position: absolute;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            pointer-events: none;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.2s;
        }

        .controls {
            margin-bottom: 20px;
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .control-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .control-group label {
            font-weight: 500;
            color: #555;
        }

        .control-group input, .control-group select {
            padding: 5px 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .btn {
            padding: 8px 16px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.2s;
        }

        .btn:hover {
            background: #0056b3;
        }

        .summary {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .summary h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }

        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .summary-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }

        .summary-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #007bff;
        }

        .summary-label {
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>ITrace 指令执行时间线可视化</h1>
        <div class="stats">
            <div>总指令数: <strong>8</strong></div>
            <div>时间范围: <strong>1000 - 1195</strong></div>
            <div>总执行时间: <strong>195</strong></div>
            <div>生成时间: <strong>2025-08-07 14:04:39</strong></div>
        </div>
    </div>

    <div class="timeline-container">
        <div class="timeline-header">
            <h3 style="margin: 0;">指令执行时间线</h3>
            <div class="legend">
                <div class="legend-item">
                    <div class="legend-color" style="background: #FF6B6B;"></div>
                    <span>Fetch</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #4ECDC4;"></div>
                    <span>Decode</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #45B7D1;"></div>
                    <span>Dispatch</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #96CEB4;"></div>
                    <span>Execute</span>
                </div>
            </div>
        </div>

        <div class="controls">
            <div class="control-group">
                <label>缩放:</label>
                <input type="range" id="zoomSlider" min="0.1" max="20" step="0.1" value="1">
                <span id="zoomValue">1.0x</span>
            </div>
            <div class="control-group">
                <label>过滤指令:</label>
                <input type="text" id="filterInput" placeholder="输入PC地址或反汇编代码...">
            </div>
            <div class="control-group">
                <label>排序:</label>
                <select id="sortSelect">
                    <option value="id">指令ID</option>
                    <option value="start_time">开始时间</option>
                    <option value="duration">执行时长</option>
                </select>
            </div>
            <button class="btn" onclick="resetView()">重置视图</button>
            <div class="control-group">
                <label>快捷键:</label>
                <span style="font-size: 12px; color: #666;">W/S: 放大/缩小 | A/D: 左移/右移</span>
            </div>
        </div>

        <div class="time-axis" id="timeAxis"></div>

        <div id="instructionList">
            
                <div class="instruction-row">
                    <div class="instruction-info">
                        <div class="instruction-id">指令 #1</div>
                        <div class="instruction-pc">0x000000001000</div>
                        <div class="instruction-disasm">tld.trii.linear.u32.global t5, (x10)</div>
                    </div>
                    <div class="timeline-bars">
                        
                    <div class="stage-bar fetch"
                         style="left: 0.00%; width: 2.56%; background-color: #FF6B6B;"
                         onmouseenter="showTooltip(event, {"name": "fetch", "start": 1000, "end": 1005, "duration": 5, "color": "#FF6B6B"}, {"id": 1, "pc": "0x000000001000", "instruction": "0x00000000fb123456", "disassembly": "tld.trii.linear.u32.global t5, (x10)", "stages": [{"name": "fetch", "start": 1000, "end": 1005, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1005, "end": 1010, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1010, "end": 1015, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 1015, "end": 1020, "duration": 5, "color": "#96CEB4"}], "total_start": 1000, "total_end": 1020, "total_duration": 20})"
                         onmouseleave="hideTooltip()">
                        fetch
                    </div>
                
                    <div class="stage-bar decode"
                         style="left: 2.56%; width: 2.56%; background-color: #4ECDC4;"
                         onmouseenter="showTooltip(event, {"name": "decode", "start": 1005, "end": 1010, "duration": 5, "color": "#4ECDC4"}, {"id": 1, "pc": "0x000000001000", "instruction": "0x00000000fb123456", "disassembly": "tld.trii.linear.u32.global t5, (x10)", "stages": [{"name": "fetch", "start": 1000, "end": 1005, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1005, "end": 1010, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1010, "end": 1015, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 1015, "end": 1020, "duration": 5, "color": "#96CEB4"}], "total_start": 1000, "total_end": 1020, "total_duration": 20})"
                         onmouseleave="hideTooltip()">
                        decode
                    </div>
                
                    <div class="stage-bar dispatch"
                         style="left: 5.13%; width: 2.56%; background-color: #45B7D1;"
                         onmouseenter="showTooltip(event, {"name": "dispatch", "start": 1010, "end": 1015, "duration": 5, "color": "#45B7D1"}, {"id": 1, "pc": "0x000000001000", "instruction": "0x00000000fb123456", "disassembly": "tld.trii.linear.u32.global t5, (x10)", "stages": [{"name": "fetch", "start": 1000, "end": 1005, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1005, "end": 1010, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1010, "end": 1015, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 1015, "end": 1020, "duration": 5, "color": "#96CEB4"}], "total_start": 1000, "total_end": 1020, "total_duration": 20})"
                         onmouseleave="hideTooltip()">
                        dispatch
                    </div>
                
                    <div class="stage-bar execute"
                         style="left: 7.69%; width: 2.56%; background-color: #96CEB4;"
                         onmouseenter="showTooltip(event, {"name": "execute", "start": 1015, "end": 1020, "duration": 5, "color": "#96CEB4"}, {"id": 1, "pc": "0x000000001000", "instruction": "0x00000000fb123456", "disassembly": "tld.trii.linear.u32.global t5, (x10)", "stages": [{"name": "fetch", "start": 1000, "end": 1005, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1005, "end": 1010, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1010, "end": 1015, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 1015, "end": 1020, "duration": 5, "color": "#96CEB4"}], "total_start": 1000, "total_end": 1020, "total_duration": 20})"
                         onmouseleave="hideTooltip()">
                        execute
                    </div>
                
                    </div>
                </div>
            

                <div class="instruction-row">
                    <div class="instruction-info">
                        <div class="instruction-id">指令 #2</div>
                        <div class="instruction-pc">0x000000001008</div>
                        <div class="instruction-disasm">tmma.ttt t3, t1, t2</div>
                    </div>
                    <div class="timeline-bars">
                        
                    <div class="stage-bar fetch"
                         style="left: 12.82%; width: 2.56%; background-color: #FF6B6B;"
                         onmouseenter="showTooltip(event, {"name": "fetch", "start": 1025, "end": 1030, "duration": 5, "color": "#FF6B6B"}, {"id": 2, "pc": "0x000000001008", "instruction": "0x00000000fb234567", "disassembly": "tmma.ttt t3, t1, t2", "stages": [{"name": "fetch", "start": 1025, "end": 1030, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1030, "end": 1035, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1035, "end": 1040, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 1040, "end": 1055, "duration": 15, "color": "#96CEB4"}], "total_start": 1025, "total_end": 1055, "total_duration": 30})"
                         onmouseleave="hideTooltip()">
                        fetch
                    </div>
                
                    <div class="stage-bar decode"
                         style="left: 15.38%; width: 2.56%; background-color: #4ECDC4;"
                         onmouseenter="showTooltip(event, {"name": "decode", "start": 1030, "end": 1035, "duration": 5, "color": "#4ECDC4"}, {"id": 2, "pc": "0x000000001008", "instruction": "0x00000000fb234567", "disassembly": "tmma.ttt t3, t1, t2", "stages": [{"name": "fetch", "start": 1025, "end": 1030, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1030, "end": 1035, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1035, "end": 1040, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 1040, "end": 1055, "duration": 15, "color": "#96CEB4"}], "total_start": 1025, "total_end": 1055, "total_duration": 30})"
                         onmouseleave="hideTooltip()">
                        decode
                    </div>
                
                    <div class="stage-bar dispatch"
                         style="left: 17.95%; width: 2.56%; background-color: #45B7D1;"
                         onmouseenter="showTooltip(event, {"name": "dispatch", "start": 1035, "end": 1040, "duration": 5, "color": "#45B7D1"}, {"id": 2, "pc": "0x000000001008", "instruction": "0x00000000fb234567", "disassembly": "tmma.ttt t3, t1, t2", "stages": [{"name": "fetch", "start": 1025, "end": 1030, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1030, "end": 1035, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1035, "end": 1040, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 1040, "end": 1055, "duration": 15, "color": "#96CEB4"}], "total_start": 1025, "total_end": 1055, "total_duration": 30})"
                         onmouseleave="hideTooltip()">
                        dispatch
                    </div>
                
                    <div class="stage-bar execute"
                         style="left: 20.51%; width: 7.69%; background-color: #96CEB4;"
                         onmouseenter="showTooltip(event, {"name": "execute", "start": 1040, "end": 1055, "duration": 15, "color": "#96CEB4"}, {"id": 2, "pc": "0x000000001008", "instruction": "0x00000000fb234567", "disassembly": "tmma.ttt t3, t1, t2", "stages": [{"name": "fetch", "start": 1025, "end": 1030, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1030, "end": 1035, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1035, "end": 1040, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 1040, "end": 1055, "duration": 15, "color": "#96CEB4"}], "total_start": 1025, "total_end": 1055, "total_duration": 30})"
                         onmouseleave="hideTooltip()">
                        execute
                    </div>
                
                    </div>
                </div>
            

                <div class="instruction-row">
                    <div class="instruction-info">
                        <div class="instruction-id">指令 #3</div>
                        <div class="instruction-pc">0x000000001010</div>
                        <div class="instruction-disasm">twait</div>
                    </div>
                    <div class="timeline-bars">
                        
                    <div class="stage-bar fetch"
                         style="left: 25.64%; width: 2.56%; background-color: #FF6B6B;"
                         onmouseenter="showTooltip(event, {"name": "fetch", "start": 1050, "end": 1055, "duration": 5, "color": "#FF6B6B"}, {"id": 3, "pc": "0x000000001010", "instruction": "0x00000000fb0012ab", "disassembly": "twait", "stages": [{"name": "fetch", "start": 1050, "end": 1055, "duration": 5, "color": "#FF6B6B"}], "total_start": 1050, "total_end": 1055, "total_duration": 5})"
                         onmouseleave="hideTooltip()">
                        fetch
                    </div>
                
                    </div>
                </div>
            

                <div class="instruction-row">
                    <div class="instruction-info">
                        <div class="instruction-id">指令 #4</div>
                        <div class="instruction-pc">0x000000001014</div>
                        <div class="instruction-disasm">ace_bsync x0</div>
                    </div>
                    <div class="timeline-bars">
                        
                    <div class="stage-bar fetch"
                         style="left: 30.77%; width: 2.56%; background-color: #FF6B6B;"
                         onmouseenter="showTooltip(event, {"name": "fetch", "start": 1060, "end": 1065, "duration": 5, "color": "#FF6B6B"}, {"id": 4, "pc": "0x000000001014", "instruction": "0x00000000fb801000", "disassembly": "ace_bsync x0", "stages": [{"name": "fetch", "start": 1060, "end": 1065, "duration": 5, "color": "#FF6B6B"}], "total_start": 1060, "total_end": 1065, "total_duration": 5})"
                         onmouseleave="hideTooltip()">
                        fetch
                    </div>
                
                    </div>
                </div>
            

                <div class="instruction-row">
                    <div class="instruction-info">
                        <div class="instruction-id">指令 #5</div>
                        <div class="instruction-pc">0x000000001018</div>
                        <div class="instruction-disasm">tcsrw.i 0x5</div>
                    </div>
                    <div class="timeline-bars">
                        
                    <div class="stage-bar fetch"
                         style="left: 35.90%; width: 2.56%; background-color: #FF6B6B;"
                         onmouseenter="showTooltip(event, {"name": "fetch", "start": 1070, "end": 1075, "duration": 5, "color": "#FF6B6B"}, {"id": 5, "pc": "0x000000001018", "instruction": "0x00000000fb345678", "disassembly": "tcsrw.i 0x5", "stages": [{"name": "fetch", "start": 1070, "end": 1075, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1075, "end": 1080, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1080, "end": 1085, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 1085, "end": 1090, "duration": 5, "color": "#96CEB4"}], "total_start": 1070, "total_end": 1090, "total_duration": 20})"
                         onmouseleave="hideTooltip()">
                        fetch
                    </div>
                
                    <div class="stage-bar decode"
                         style="left: 38.46%; width: 2.56%; background-color: #4ECDC4;"
                         onmouseenter="showTooltip(event, {"name": "decode", "start": 1075, "end": 1080, "duration": 5, "color": "#4ECDC4"}, {"id": 5, "pc": "0x000000001018", "instruction": "0x00000000fb345678", "disassembly": "tcsrw.i 0x5", "stages": [{"name": "fetch", "start": 1070, "end": 1075, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1075, "end": 1080, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1080, "end": 1085, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 1085, "end": 1090, "duration": 5, "color": "#96CEB4"}], "total_start": 1070, "total_end": 1090, "total_duration": 20})"
                         onmouseleave="hideTooltip()">
                        decode
                    </div>
                
                    <div class="stage-bar dispatch"
                         style="left: 41.03%; width: 2.56%; background-color: #45B7D1;"
                         onmouseenter="showTooltip(event, {"name": "dispatch", "start": 1080, "end": 1085, "duration": 5, "color": "#45B7D1"}, {"id": 5, "pc": "0x000000001018", "instruction": "0x00000000fb345678", "disassembly": "tcsrw.i 0x5", "stages": [{"name": "fetch", "start": 1070, "end": 1075, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1075, "end": 1080, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1080, "end": 1085, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 1085, "end": 1090, "duration": 5, "color": "#96CEB4"}], "total_start": 1070, "total_end": 1090, "total_duration": 20})"
                         onmouseleave="hideTooltip()">
                        dispatch
                    </div>
                
                    <div class="stage-bar execute"
                         style="left: 43.59%; width: 2.56%; background-color: #96CEB4;"
                         onmouseenter="showTooltip(event, {"name": "execute", "start": 1085, "end": 1090, "duration": 5, "color": "#96CEB4"}, {"id": 5, "pc": "0x000000001018", "instruction": "0x00000000fb345678", "disassembly": "tcsrw.i 0x5", "stages": [{"name": "fetch", "start": 1070, "end": 1075, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1075, "end": 1080, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1080, "end": 1085, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 1085, "end": 1090, "duration": 5, "color": "#96CEB4"}], "total_start": 1070, "total_end": 1090, "total_duration": 20})"
                         onmouseleave="hideTooltip()">
                        execute
                    </div>
                
                    </div>
                </div>
            

                <div class="instruction-row">
                    <div class="instruction-info">
                        <div class="instruction-id">指令 #6</div>
                        <div class="instruction-pc">0x000000001020</div>
                        <div class="instruction-disasm">tld.linear.u16 t6, (x11)</div>
                    </div>
                    <div class="timeline-bars">
                        
                    <div class="stage-bar fetch"
                         style="left: 48.72%; width: 2.56%; background-color: #FF6B6B;"
                         onmouseenter="showTooltip(event, {"name": "fetch", "start": 1095, "end": 1100, "duration": 5, "color": "#FF6B6B"}, {"id": 6, "pc": "0x000000001020", "instruction": "0x00000000fb456789", "disassembly": "tld.linear.u16 t6, (x11)", "stages": [{"name": "fetch", "start": 1095, "end": 1100, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1100, "end": 1105, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1105, "end": 1110, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 1110, "end": 1125, "duration": 15, "color": "#96CEB4"}], "total_start": 1095, "total_end": 1125, "total_duration": 30})"
                         onmouseleave="hideTooltip()">
                        fetch
                    </div>
                
                    <div class="stage-bar decode"
                         style="left: 51.28%; width: 2.56%; background-color: #4ECDC4;"
                         onmouseenter="showTooltip(event, {"name": "decode", "start": 1100, "end": 1105, "duration": 5, "color": "#4ECDC4"}, {"id": 6, "pc": "0x000000001020", "instruction": "0x00000000fb456789", "disassembly": "tld.linear.u16 t6, (x11)", "stages": [{"name": "fetch", "start": 1095, "end": 1100, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1100, "end": 1105, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1105, "end": 1110, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 1110, "end": 1125, "duration": 15, "color": "#96CEB4"}], "total_start": 1095, "total_end": 1125, "total_duration": 30})"
                         onmouseleave="hideTooltip()">
                        decode
                    </div>
                
                    <div class="stage-bar dispatch"
                         style="left: 53.85%; width: 2.56%; background-color: #45B7D1;"
                         onmouseenter="showTooltip(event, {"name": "dispatch", "start": 1105, "end": 1110, "duration": 5, "color": "#45B7D1"}, {"id": 6, "pc": "0x000000001020", "instruction": "0x00000000fb456789", "disassembly": "tld.linear.u16 t6, (x11)", "stages": [{"name": "fetch", "start": 1095, "end": 1100, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1100, "end": 1105, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1105, "end": 1110, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 1110, "end": 1125, "duration": 15, "color": "#96CEB4"}], "total_start": 1095, "total_end": 1125, "total_duration": 30})"
                         onmouseleave="hideTooltip()">
                        dispatch
                    </div>
                
                    <div class="stage-bar execute"
                         style="left: 56.41%; width: 7.69%; background-color: #96CEB4;"
                         onmouseenter="showTooltip(event, {"name": "execute", "start": 1110, "end": 1125, "duration": 15, "color": "#96CEB4"}, {"id": 6, "pc": "0x000000001020", "instruction": "0x00000000fb456789", "disassembly": "tld.linear.u16 t6, (x11)", "stages": [{"name": "fetch", "start": 1095, "end": 1100, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1100, "end": 1105, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1105, "end": 1110, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 1110, "end": 1125, "duration": 15, "color": "#96CEB4"}], "total_start": 1095, "total_end": 1125, "total_duration": 30})"
                         onmouseleave="hideTooltip()">
                        execute
                    </div>
                
                    </div>
                </div>
            

                <div class="instruction-row">
                    <div class="instruction-info">
                        <div class="instruction-id">指令 #7</div>
                        <div class="instruction-pc">0x000000001028</div>
                        <div class="instruction-disasm">tmma.tnt t4, t2, t3</div>
                    </div>
                    <div class="timeline-bars">
                        
                    <div class="stage-bar fetch"
                         style="left: 66.67%; width: 2.56%; background-color: #FF6B6B;"
                         onmouseenter="showTooltip(event, {"name": "fetch", "start": 1130, "end": 1135, "duration": 5, "color": "#FF6B6B"}, {"id": 7, "pc": "0x000000001028", "instruction": "0x00000000fb567890", "disassembly": "tmma.tnt t4, t2, t3", "stages": [{"name": "fetch", "start": 1130, "end": 1135, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1135, "end": 1140, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1140, "end": 1145, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 1145, "end": 1165, "duration": 20, "color": "#96CEB4"}], "total_start": 1130, "total_end": 1165, "total_duration": 35})"
                         onmouseleave="hideTooltip()">
                        fetch
                    </div>
                
                    <div class="stage-bar decode"
                         style="left: 69.23%; width: 2.56%; background-color: #4ECDC4;"
                         onmouseenter="showTooltip(event, {"name": "decode", "start": 1135, "end": 1140, "duration": 5, "color": "#4ECDC4"}, {"id": 7, "pc": "0x000000001028", "instruction": "0x00000000fb567890", "disassembly": "tmma.tnt t4, t2, t3", "stages": [{"name": "fetch", "start": 1130, "end": 1135, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1135, "end": 1140, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1140, "end": 1145, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 1145, "end": 1165, "duration": 20, "color": "#96CEB4"}], "total_start": 1130, "total_end": 1165, "total_duration": 35})"
                         onmouseleave="hideTooltip()">
                        decode
                    </div>
                
                    <div class="stage-bar dispatch"
                         style="left: 71.79%; width: 2.56%; background-color: #45B7D1;"
                         onmouseenter="showTooltip(event, {"name": "dispatch", "start": 1140, "end": 1145, "duration": 5, "color": "#45B7D1"}, {"id": 7, "pc": "0x000000001028", "instruction": "0x00000000fb567890", "disassembly": "tmma.tnt t4, t2, t3", "stages": [{"name": "fetch", "start": 1130, "end": 1135, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1135, "end": 1140, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1140, "end": 1145, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 1145, "end": 1165, "duration": 20, "color": "#96CEB4"}], "total_start": 1130, "total_end": 1165, "total_duration": 35})"
                         onmouseleave="hideTooltip()">
                        dispatch
                    </div>
                
                    <div class="stage-bar execute"
                         style="left: 74.36%; width: 10.26%; background-color: #96CEB4;"
                         onmouseenter="showTooltip(event, {"name": "execute", "start": 1145, "end": 1165, "duration": 20, "color": "#96CEB4"}, {"id": 7, "pc": "0x000000001028", "instruction": "0x00000000fb567890", "disassembly": "tmma.tnt t4, t2, t3", "stages": [{"name": "fetch", "start": 1130, "end": 1135, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1135, "end": 1140, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1140, "end": 1145, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 1145, "end": 1165, "duration": 20, "color": "#96CEB4"}], "total_start": 1130, "total_end": 1165, "total_duration": 35})"
                         onmouseleave="hideTooltip()">
                        execute
                    </div>
                
                    </div>
                </div>
            

                <div class="instruction-row">
                    <div class="instruction-info">
                        <div class="instruction-id">指令 #8</div>
                        <div class="instruction-pc">0x000000001030</div>
                        <div class="instruction-disasm">tst.linear.u32 (x12), t7</div>
                    </div>
                    <div class="timeline-bars">
                        
                    <div class="stage-bar fetch"
                         style="left: 87.18%; width: 2.56%; background-color: #FF6B6B;"
                         onmouseenter="showTooltip(event, {"name": "fetch", "start": 1170, "end": 1175, "duration": 5, "color": "#FF6B6B"}, {"id": 8, "pc": "0x000000001030", "instruction": "0x00000000fb678901", "disassembly": "tst.linear.u32 (x12), t7", "stages": [{"name": "fetch", "start": 1170, "end": 1175, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1175, "end": 1180, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1180, "end": 1185, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 1185, "end": 1195, "duration": 10, "color": "#96CEB4"}], "total_start": 1170, "total_end": 1195, "total_duration": 25})"
                         onmouseleave="hideTooltip()">
                        fetch
                    </div>
                
                    <div class="stage-bar decode"
                         style="left: 89.74%; width: 2.56%; background-color: #4ECDC4;"
                         onmouseenter="showTooltip(event, {"name": "decode", "start": 1175, "end": 1180, "duration": 5, "color": "#4ECDC4"}, {"id": 8, "pc": "0x000000001030", "instruction": "0x00000000fb678901", "disassembly": "tst.linear.u32 (x12), t7", "stages": [{"name": "fetch", "start": 1170, "end": 1175, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1175, "end": 1180, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1180, "end": 1185, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 1185, "end": 1195, "duration": 10, "color": "#96CEB4"}], "total_start": 1170, "total_end": 1195, "total_duration": 25})"
                         onmouseleave="hideTooltip()">
                        decode
                    </div>
                
                    <div class="stage-bar dispatch"
                         style="left: 92.31%; width: 2.56%; background-color: #45B7D1;"
                         onmouseenter="showTooltip(event, {"name": "dispatch", "start": 1180, "end": 1185, "duration": 5, "color": "#45B7D1"}, {"id": 8, "pc": "0x000000001030", "instruction": "0x00000000fb678901", "disassembly": "tst.linear.u32 (x12), t7", "stages": [{"name": "fetch", "start": 1170, "end": 1175, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1175, "end": 1180, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1180, "end": 1185, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 1185, "end": 1195, "duration": 10, "color": "#96CEB4"}], "total_start": 1170, "total_end": 1195, "total_duration": 25})"
                         onmouseleave="hideTooltip()">
                        dispatch
                    </div>
                
                    <div class="stage-bar execute"
                         style="left: 94.87%; width: 5.13%; background-color: #96CEB4;"
                         onmouseenter="showTooltip(event, {"name": "execute", "start": 1185, "end": 1195, "duration": 10, "color": "#96CEB4"}, {"id": 8, "pc": "0x000000001030", "instruction": "0x00000000fb678901", "disassembly": "tst.linear.u32 (x12), t7", "stages": [{"name": "fetch", "start": 1170, "end": 1175, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1175, "end": 1180, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1180, "end": 1185, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 1185, "end": 1195, "duration": 10, "color": "#96CEB4"}], "total_start": 1170, "total_end": 1195, "total_duration": 25})"
                         onmouseleave="hideTooltip()">
                        execute
                    </div>
                
                    </div>
                </div>
            
        </div>
    </div>

    <div class="tooltip" id="tooltip"></div>

    <div class="summary">
        <h3>执行统计摘要</h3>
        <div class="summary-grid">
            
            <div class="summary-item">
                <div class="summary-value">8</div>
                <div class="summary-label">总指令数</div>
            </div>
            <div class="summary-item">
                <div class="summary-value">21.2</div>
                <div class="summary-label">平均执行时间</div>
            </div>
            <div class="summary-item">
                <div class="summary-value">35</div>
                <div class="summary-label">最长执行时间</div>
            </div>
            <div class="summary-item">
                <div class="summary-value">5</div>
                <div class="summary-label">最短执行时间</div>
            </div>
            <div class="summary-item">
                <div class="summary-value">5.0</div>
                <div class="summary-label">平均Fetch时间</div>
            </div>
            <div class="summary-item">
                <div class="summary-value">5.0</div>
                <div class="summary-label">平均Decode时间</div>
            </div>
            <div class="summary-item">
                <div class="summary-value">5.0</div>
                <div class="summary-label">平均Dispatch时间</div>
            </div>
            <div class="summary-item">
                <div class="summary-value">11.7</div>
                <div class="summary-label">平均Execute时间</div>
            </div>
        
        </div>
    </div>

    <script>
        const instructions = [{"id": 1, "pc": "0x000000001000", "instruction": "0x00000000fb123456", "disassembly": "tld.trii.linear.u32.global t5, (x10)", "stages": [{"name": "fetch", "start": 1000, "end": 1005, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1005, "end": 1010, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1010, "end": 1015, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 1015, "end": 1020, "duration": 5, "color": "#96CEB4"}], "total_start": 1000, "total_end": 1020, "total_duration": 20}, {"id": 2, "pc": "0x000000001008", "instruction": "0x00000000fb234567", "disassembly": "tmma.ttt t3, t1, t2", "stages": [{"name": "fetch", "start": 1025, "end": 1030, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1030, "end": 1035, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1035, "end": 1040, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 1040, "end": 1055, "duration": 15, "color": "#96CEB4"}], "total_start": 1025, "total_end": 1055, "total_duration": 30}, {"id": 3, "pc": "0x000000001010", "instruction": "0x00000000fb0012ab", "disassembly": "twait", "stages": [{"name": "fetch", "start": 1050, "end": 1055, "duration": 5, "color": "#FF6B6B"}], "total_start": 1050, "total_end": 1055, "total_duration": 5}, {"id": 4, "pc": "0x000000001014", "instruction": "0x00000000fb801000", "disassembly": "ace_bsync x0", "stages": [{"name": "fetch", "start": 1060, "end": 1065, "duration": 5, "color": "#FF6B6B"}], "total_start": 1060, "total_end": 1065, "total_duration": 5}, {"id": 5, "pc": "0x000000001018", "instruction": "0x00000000fb345678", "disassembly": "tcsrw.i 0x5", "stages": [{"name": "fetch", "start": 1070, "end": 1075, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1075, "end": 1080, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1080, "end": 1085, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 1085, "end": 1090, "duration": 5, "color": "#96CEB4"}], "total_start": 1070, "total_end": 1090, "total_duration": 20}, {"id": 6, "pc": "0x000000001020", "instruction": "0x00000000fb456789", "disassembly": "tld.linear.u16 t6, (x11)", "stages": [{"name": "fetch", "start": 1095, "end": 1100, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1100, "end": 1105, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1105, "end": 1110, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 1110, "end": 1125, "duration": 15, "color": "#96CEB4"}], "total_start": 1095, "total_end": 1125, "total_duration": 30}, {"id": 7, "pc": "0x000000001028", "instruction": "0x00000000fb567890", "disassembly": "tmma.tnt t4, t2, t3", "stages": [{"name": "fetch", "start": 1130, "end": 1135, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1135, "end": 1140, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1140, "end": 1145, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 1145, "end": 1165, "duration": 20, "color": "#96CEB4"}], "total_start": 1130, "total_end": 1165, "total_duration": 35}, {"id": 8, "pc": "0x000000001030", "instruction": "0x00000000fb678901", "disassembly": "tst.linear.u32 (x12), t7", "stages": [{"name": "fetch", "start": 1170, "end": 1175, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1175, "end": 1180, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1180, "end": 1185, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 1185, "end": 1195, "duration": 10, "color": "#96CEB4"}], "total_start": 1170, "total_end": 1195, "total_duration": 25}];
        const minTime = 1000;
        const maxTime = 1195;
        const timeRange = maxTime - minTime;

        let currentZoom = 1.0;
        let currentFilter = '';
        let currentSort = 'id';
        let currentScrollLeft = 0;

        // 初始化时间轴
        function initTimeAxis() {
            const timeAxis = document.getElementById('timeAxis');
            timeAxis.innerHTML = ''; // 清空现有内容
            const tickCount = 10;

            for (let i = 0; i <= tickCount; i++) {
                const time = minTime + (timeRange * i / tickCount);
                const position = (i / tickCount) * 100 * currentZoom;

                const tick = document.createElement('div');
                tick.className = 'time-tick';
                tick.style.left = position + '%';
                timeAxis.appendChild(tick);

                const label = document.createElement('div');
                label.className = 'time-label';
                label.style.left = position + '%';
                label.textContent = Math.round(time);
                timeAxis.appendChild(label);
            }
        }

        // 计算时间条的位置和宽度
        function calculateBarPosition(start, end) {
            const startPercent = ((start - minTime) / timeRange) * 100 * currentZoom;
            const widthPercent = ((end - start) / timeRange) * 100 * currentZoom;
            return { left: startPercent, width: Math.max(widthPercent, 0.5) };
        }

        // 显示工具提示
        function showTooltip(event, stage, instruction) {
            const tooltip = document.getElementById('tooltip');
            tooltip.innerHTML = `
                <strong>${stage.name.toUpperCase()}</strong><br>
                指令: ${instruction.disassembly}<br>
                开始: ${stage.start}<br>
                结束: ${stage.end}<br>
                持续: ${stage.duration}
            `;
            tooltip.style.left = event.pageX + 10 + 'px';
            tooltip.style.top = event.pageY - 10 + 'px';
            tooltip.style.opacity = '1';
        }

        // 隐藏工具提示
        function hideTooltip() {
            document.getElementById('tooltip').style.opacity = '0';
        }

        // 渲染指令列表
        function renderInstructions(filteredInstructions = null) {
            const instructionList = document.getElementById('instructionList');
            const instructionsToRender = filteredInstructions || instructions;

            instructionList.innerHTML = '';

            instructionsToRender.forEach(instruction => {
                const row = document.createElement('div');
                row.className = 'instruction-row';

                const info = document.createElement('div');
                info.className = 'instruction-info';
                info.innerHTML = `
                    <div class="instruction-id">指令 #${instruction.id}</div>
                    <div class="instruction-pc">${instruction.pc}</div>
                    <div class="instruction-disasm">${instruction.disassembly}</div>
                `;

                const timeline = document.createElement('div');
                timeline.className = 'timeline-bars';

                instruction.stages.forEach(stage => {
                    const bar = document.createElement('div');
                    bar.className = `stage-bar ${stage.name}`;
                    bar.style.backgroundColor = stage.color;

                    const position = calculateBarPosition(stage.start, stage.end);
                    bar.style.left = position.left + '%';
                    bar.style.width = position.width + '%';

                    if (position.width > 2) {
                        bar.textContent = stage.name;
                    }

                    bar.addEventListener('mouseenter', (e) => showTooltip(e, stage, instruction));
                    bar.addEventListener('mouseleave', hideTooltip);

                    timeline.appendChild(bar);
                });

                row.appendChild(info);
                row.appendChild(timeline);
                instructionList.appendChild(row);
            });
        }

        // 过滤指令
        function filterInstructions() {
            const filter = document.getElementById('filterInput').value.toLowerCase();
            if (!filter) {
                renderInstructions();
                return;
            }

            const filtered = instructions.filter(instr =>
                instr.pc.toLowerCase().includes(filter) ||
                instr.disassembly.toLowerCase().includes(filter)
            );

            renderInstructions(filtered);
        }

        // 排序指令
        function sortInstructions() {
            const sortBy = document.getElementById('sortSelect').value;
            const sorted = [...instructions];

            sorted.sort((a, b) => {
                switch(sortBy) {
                    case 'start_time':
                        return a.total_start - b.total_start;
                    case 'duration':
                        return b.total_duration - a.total_duration;
                    default:
                        return a.id - b.id;
                }
            });

            renderInstructions(sorted);
        }

        // 缩放功能
        function updateZoom() {
            const zoom = parseFloat(document.getElementById('zoomSlider').value);
            currentZoom = zoom;
            document.getElementById('zoomValue').textContent = zoom.toFixed(1) + 'x';

            // 更新时间轴和时间线容器的宽度以支持水平滚动
            const newWidth = (800 * zoom) + 'px';

            const timeAxis = document.getElementById('timeAxis');
            timeAxis.style.minWidth = newWidth;

            const timelineBars = document.querySelectorAll('.timeline-bars');
            timelineBars.forEach(bar => {
                bar.style.minWidth = newWidth;
            });

            // 重新渲染时间轴和指令以应用缩放
            initTimeAxis();
            renderInstructions();
        }

        // 重置视图
        function resetView() {
            document.getElementById('zoomSlider').value = 1;
            document.getElementById('filterInput').value = '';
            document.getElementById('sortSelect').value = 'id';
            currentScrollLeft = 0;
            updateZoom();
            renderInstructions();
            // 重置滚动位置
            const timelineContainer = document.querySelector('.timeline-container');
            timelineContainer.scrollLeft = 0;
        }

        // 键盘快捷键处理
        function handleKeyPress(event) {
            // 如果用户正在输入框中输入，不处理快捷键
            if (event.target.tagName === 'INPUT' || event.target.tagName === 'SELECT') {
                return;
            }

            const zoomSlider = document.getElementById('zoomSlider');
            const timelineContainer = document.querySelector('.timeline-container');
            const scrollStep = 50; // 滚动步长
            const zoomStep = 0.2; // 缩放步长

            switch(event.key.toLowerCase()) {
                case 'w': // 放大
                    event.preventDefault();
                    const newZoomUp = Math.min(parseFloat(zoomSlider.value) + zoomStep, 20);
                    zoomSlider.value = newZoomUp;
                    updateZoom();
                    break;

                case 's': // 缩小
                    event.preventDefault();
                    const newZoomDown = Math.max(parseFloat(zoomSlider.value) - zoomStep, 0.1);
                    zoomSlider.value = newZoomDown;
                    updateZoom();
                    break;

                case 'a': // 左移
                    event.preventDefault();
                    timelineContainer.scrollLeft = Math.max(timelineContainer.scrollLeft - scrollStep, 0);
                    currentScrollLeft = timelineContainer.scrollLeft;
                    break;

                case 'd': // 右移
                    event.preventDefault();
                    timelineContainer.scrollLeft = Math.min(
                        timelineContainer.scrollLeft + scrollStep,
                        timelineContainer.scrollWidth - timelineContainer.clientWidth
                    );
                    currentScrollLeft = timelineContainer.scrollLeft;
                    break;
            }
        }

        // 事件监听器
        document.getElementById('zoomSlider').addEventListener('input', updateZoom);
        document.getElementById('filterInput').addEventListener('input', filterInstructions);
        document.getElementById('sortSelect').addEventListener('change', sortInstructions);

        // 键盘事件监听器
        document.addEventListener('keydown', handleKeyPress);

        // 初始化
        initTimeAxis();
        renderInstructions();
    </script>
</body>
</html>
        