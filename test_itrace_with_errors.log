InstrID,PC,Instruction,Disassembly,FetchStartTime,DecodeStartTime,DispatchStartTime,ExecuteStartTime,ExecuteEndTime
1,0x000000001000,0x00000000fb123456,"tld.trii.linear.u32.global t5, (x10)",1000,1005,1010,1015,1020
2,0x000000001008,0x00000000fb234567,"tmma.ttt t3, t1, t2",1025,1030,1035,1040,1055
3,0x000000001010,0x00000000fb0012ab,"twait",1050,1055,0,0,0
4,0x000000001014,0x00000000fb801000,"ace_bsync x0",1060,1065,0,0,0
5,0x000000001018,0x00000000fb345678,"tcsrw.i 0x5",1070,1075,1080,0,0
6,0x000000001020,0x00000000fb456789,"tld.linear.u16 t6, (x11)",1095,1100,0,0,0

// ERROR: 4 instructions were not completed at simulation end:
// Incomplete Instr: ID=3, PC=0x000000001010, Instr=0x00000000fb0012ab, Disasm="twait" - STUCK in DISPATCH stage
// Incomplete Instr: ID=4, PC=0x000000001014, Instr=0x00000000fb801000, Disasm="ace_bsync x0" - STUCK in DISPATCH stage
// Incomplete Instr: ID=5, PC=0x000000001018, Instr=0x00000000fb345678, Disasm="tcsrw.i 0x5" - STUCK in EXECUTE stage
// Incomplete Instr: ID=6, PC=0x000000001020, Instr=0x00000000fb456789, Disasm="tld.linear.u16 t6, (x11)" - STUCK between FETCH and DISPATCH
